using GMT

# Download data from:
model = gmtread("USTClitho2.0.wrst.sea_level.txt");

# Create two data cubes (grids) with the Vp and Vs velocities
Cvp = xyzw2cube(model);
Cvs = xyzw2cube(model, zcol=5);

# Add names to the cube layers to be used as titles in next figure
Cvp.names = ["Depth = $(Int(i)) km" for i in Cvp.v];
Cvs.names = ["Depth = $(Int(i)) km" for i in Cvs.v];

# 创建 Vp 和 Vs 模型的 3D 立方体图（并排显示）
cubeplot(Cvp, top="@earth_relief_05m", colorbar=("xlabel=P-wave velocity", "ylabel=km/s"), zdown=true, title="Vp model")
cubeplot!(Cvs, top="@earth_relief_05m", topshade=true, zdown=true, colorbar=("xlabel=S-wave velocity", "ylabel=km/s"), xshift=18, title="Vs model", show=true)


# 创建带有插图的 Vp 模型立方体图
cubeplot(Cvp, top="@earth_relief", inset=(lon=100, lat=35), topshade=true, zdown=true,
         colorbar=("xlabel=P-wave velocity", "ylabel=km/s"), show=true)

