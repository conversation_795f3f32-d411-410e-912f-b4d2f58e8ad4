<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Frameset//EN""http://www.w3.org/TR/REC-html40/frameset.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc on Sat Nov 03 18:12:05 CET 2001 -->
<TITLE>
: Index
</TITLE>
<LINK REL ="stylesheet" TYPE="text/css" HREF="stylesheet.css" TITLE="Style">
</HEAD>
<BODY BGCOLOR="white">

<!-- ========== START OF NAVBAR ========== -->
<A NAME="navbar_top"><!-- --></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Index</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html" TARGET="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="index-all.html" TARGET="_top"><B>NO FRAMES</B></A></FONT></TD>
</TR>
</TABLE>
<!-- =========== END OF NAVBAR =========== -->

<A HREF="#_G_">G</A> <A HREF="#_N_">N</A> <A HREF="#_V_">V</A> <HR>
<A NAME="_G_"><!-- --></A><H2>
<B>G</B></H2>
<DL>
<DT><A HREF="gnu/gettext/GettextResource.html#gettext(java.util.ResourceBundle, java.lang.String)"><B>gettext(ResourceBundle, String)</B></A> - 
Static method in class gnu.gettext.<A HREF="gnu/gettext/GettextResource.html">GettextResource</A>
<DD>Returns the translation of <VAR>msgid</VAR>.
<DT><A HREF="gnu/gettext/GettextResource.html"><B>GettextResource</B></A> - class gnu.gettext.<A HREF="gnu/gettext/GettextResource.html">GettextResource</A>.<DD>This class implements the main GNU libintl functions in Java.
 <DT><A HREF="gnu/gettext/GettextResource.html#GettextResource()"><B>GettextResource()</B></A> - 
Constructor for class gnu.gettext.<A HREF="gnu/gettext/GettextResource.html">GettextResource</A>
<DD>&nbsp;
</DL>
<HR>
<A NAME="_N_"><!-- --></A><H2>
<B>N</B></H2>
<DL>
<DT><A HREF="gnu/gettext/GettextResource.html#ngettext(java.util.ResourceBundle, java.lang.String, java.lang.String, long)"><B>ngettext(ResourceBundle, String, String, long)</B></A> - 
Static method in class gnu.gettext.<A HREF="gnu/gettext/GettextResource.html">GettextResource</A>
<DD>Returns the plural form for <VAR>n</VAR> of the translation of
 <VAR>msgid</VAR>.
</DL>
<HR>
<A NAME="_V_"><!-- --></A><H2>
<B>V</B></H2>
<DL>
<DT><A HREF="gnu/gettext/GettextResource.html#verbose"><B>verbose</B></A> - 
Static variable in class gnu.gettext.<A HREF="gnu/gettext/GettextResource.html">GettextResource</A>
<DD>&nbsp;
</DL>
<HR>
<A HREF="#_G_">G</A> <A HREF="#_N_">N</A> <A HREF="#_V_">V</A> 
<!-- ========== START OF NAVBAR ========== -->
<A NAME="navbar_bottom"><!-- --></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Index</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html" TARGET="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="index-all.html" TARGET="_top"><B>NO FRAMES</B></A></FONT></TD>
</TR>
</TABLE>
<!-- =========== END OF NAVBAR =========== -->

<HR>

</BODY>
</HTML>
