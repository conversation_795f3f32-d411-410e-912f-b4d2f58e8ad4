<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ENVSUBST</title>

</head>
<body>

<h1 align="center">ENVSUBST</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#AUTHOR">AUTHOR</a><br>
<a href="#REPORTING BUGS">REPORTING BUGS</a><br>
<a href="#COPYRIGHT">COPYRIGHT</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">envsubst
- substitutes environment variables in shell format
strings</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>envsubst</b>
[<i>OPTION</i>] [<i>SHELL-FORMAT</i>]</p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Substitutes the
values of environment variables.</p>

<p style="margin-left:11%; margin-top: 1em"><b>Operation
mode: <br>
-v</b>, <b>--variables</b></p>

<p style="margin-left:22%;">output the variables occurring
in SHELL-FORMAT</p>

<p style="margin-left:11%; margin-top: 1em"><b>Informative
output: <br>
-h</b>, <b>--help</b></p>

<p style="margin-left:22%;">display this help and exit</p>

<p style="margin-left:11%;"><b>-V</b>,
<b>--version</b></p>

<p style="margin-left:22%;">output version information and
exit</p>

<p style="margin-left:11%; margin-top: 1em">In normal
operation mode, standard input is copied to standard output,
with references to environment variables of the form
$VARIABLE or ${VARIABLE} being replaced with the
corresponding values. If a SHELL-FORMAT is given, only
those environment variables that are referenced in
SHELL-FORMAT are substituted; otherwise all
environment variables references occurring in standard input
are substituted.</p>

<p style="margin-left:11%; margin-top: 1em">When
<b>--variables</b> is used, standard input is
ignored, and the output consists of the environment
variables that are referenced in SHELL-FORMAT, one per
line.</p>

<h2>AUTHOR
<a name="AUTHOR"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Written by
Bruno Haible.</p>

<h2>REPORTING BUGS
<a name="REPORTING BUGS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Report bugs in
the bug tracker at
&lt;https://savannah.gnu.org/projects/gettext&gt; or by
email to &lt;<EMAIL>&gt;.</p>

<h2>COPYRIGHT
<a name="COPYRIGHT"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Copyright
&copy; 2003-2023 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later
&lt;https://gnu.org/licenses/gpl.html&gt; <br>
This is free software: you are free to change and
redistribute it. There is NO WARRANTY, to the extent
permitted by law.</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The full
documentation for <b>envsubst</b> is maintained as a Texinfo
manual. If the <b>info</b> and <b>envsubst</b> programs are
properly installed at your site, the command</p>

<p style="margin-left:22%; margin-top: 1em"><b>info
envsubst</b></p>

<p style="margin-left:11%; margin-top: 1em">should give you
access to the complete manual.</p>
<hr>
</body>
</html>
