.\" DO NOT MODIFY THIS FILE!  It was generated by help2man 1.47.6.
.TH ENVSUBST "1" "September 2023" "GNU gettext-runtime 0.22.2" "User Commands"
.SH NAME
envsubst \- substitutes environment variables in shell format strings
.SH SYNOPSIS
.B envsubst
[\fI\,OPTION\/\fR] [\fI\,SHELL-FORMAT\/\fR]
.SH DESCRIPTION
.\" Add any additional description here
.PP
Substitutes the values of environment variables.
.SS "Operation mode:"
.TP
\fB\-v\fR, \fB\-\-variables\fR
output the variables occurring in SHELL\-FORMAT
.SS "Informative output:"
.TP
\fB\-h\fR, \fB\-\-help\fR
display this help and exit
.TP
\fB\-V\fR, \fB\-\-version\fR
output version information and exit
.PP
In normal operation mode, standard input is copied to standard output,
with references to environment variables of the form $VARIABLE or ${VARIABLE}
being replaced with the corresponding values.  If a SHELL\-FORMAT is given,
only those environment variables that are referenced in SHELL\-FORMAT are
substituted; otherwise all environment variables references occurring in
standard input are substituted.
.PP
When \fB\-\-variables\fR is used, standard input is ignored, and the output consists
of the environment variables that are referenced in SHELL\-FORMAT, one per line.
.SH AUTHOR
Written by Bruno Haible.
.SH "REPORTING BUGS"
Report bugs in the bug tracker at <https://savannah.gnu.org/projects/gettext>
or by email to <bug\-<EMAIL>>.
.SH COPYRIGHT
Copyright \(co 2003\-2023 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>
.br
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.
.SH "SEE ALSO"
The full documentation for
.B envsubst
is maintained as a Texinfo manual.  If the
.B info
and
.B envsubst
programs are properly installed at your site, the command
.IP
.B info envsubst
.PP
should give you access to the complete manual.
