.\" DO NOT MODIFY THIS FILE!  It was generated by help2man 1.47.6.
.TH GETTEXT "1" "September 2023" "GNU gettext-runtime 0.22.2" "User Commands"
.SH NAME
gettext \- translate message
.SH SYNOPSIS
.B gettext
[\fI\,OPTION\/\fR] [[\fI\,TEXTDOMAIN\/\fR] \fI\,MSGID\/\fR]
.br
.B gettext
[\fI\,OPTION\/\fR] \fI\,-s \/\fR[\fI\,MSGID\/\fR]...
.SH DESCRIPTION
.\" Add any additional description here
The \fBgettext\fP program translates a natural language message into the
user's language, by looking up the translation in a message catalog.
.PP
Display native language translation of a textual message.
.TP
\fB\-d\fR, \fB\-\-domain\fR=\fI\,TEXTDOMAIN\/\fR
retrieve translated messages from TEXTDOMAIN
.TP
\fB\-c\fR, \fB\-\-context\fR=\fI\,CONTEXT\/\fR
specify context for MSGID
.TP
\fB\-e\fR
enable expansion of some escape sequences
.TP
\fB\-n\fR
suppress trailing newline
.TP
\fB\-E\fR
(ignored for compatibility)
.TP
[TEXTDOMAIN] MSGID
retrieve translated message corresponding
to MSGID from TEXTDOMAIN
.SS "Informative output:"
.TP
\fB\-h\fR, \fB\-\-help\fR
display this help and exit
.TP
\fB\-V\fR, \fB\-\-version\fR
display version information and exit
.PP
If the TEXTDOMAIN parameter is not given, the domain is determined from the
environment variable TEXTDOMAIN.  If the message catalog is not found in the
regular directory, another location can be specified with the environment
variable TEXTDOMAINDIR.
When used with the \fB\-s\fR option the program behaves like the 'echo' command.
But it does not simply copy its arguments to stdout.  Instead those messages
found in the selected catalog are translated.
Standard search directory: /workspace/destdir/share/locale
.SH AUTHOR
Written by Ulrich Drepper.
.SH "REPORTING BUGS"
Report bugs in the bug tracker at <https://savannah.gnu.org/projects/gettext>
or by email to <bug\-<EMAIL>>.
.SH COPYRIGHT
Copyright \(co 1995\-2023 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>
.br
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.
.SH "SEE ALSO"
The full documentation for
.B gettext
is maintained as a Texinfo manual.  If the
.B info
and
.B gettext
programs are properly installed at your site, the command
.IP
.B info gettext
.PP
should give you access to the complete manual.
