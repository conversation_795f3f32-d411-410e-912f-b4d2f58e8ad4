.\" DO NOT MODIFY THIS FILE!  It was generated by help2man 1.47.6.
.TH NGETTEXT "1" "September 2023" "GNU gettext-runtime 0.22.2" "User Commands"
.SH NAME
ngettext \- translate message and choose plural form
.SH SYNOPSIS
.B ngettext
[\fI\,OPTION\/\fR] [\fI\,TEXTDOMAIN\/\fR] \fI\,MSGID MSGID-PLURAL COUNT\/\fR
.SH DESCRIPTION
.\" Add any additional description here
The \fBngettext\fP program translates a natural language message into the
user's language, by looking up the translation in a message catalog, and
chooses the appropriate plural form, which depends on the number \fICOUNT\fP
and the language of the message catalog where the translation was found.
.PP
Display native language translation of a textual message whose grammatical
form depends on a number.
.TP
\fB\-d\fR, \fB\-\-domain\fR=\fI\,TEXTDOMAIN\/\fR
retrieve translated message from TEXTDOMAIN
.TP
\fB\-c\fR, \fB\-\-context\fR=\fI\,CONTEXT\/\fR
specify context for MSGID
.TP
\fB\-e\fR
enable expansion of some escape sequences
.TP
\fB\-E\fR
(ignored for compatibility)
.TP
[TEXTDOMAIN]
retrieve translated message from TEXTDOMAIN
.TP
MSGID MSGID\-PLURAL
translate MSGID (singular) / MSGID\-PLURAL (plural)
.TP
COUNT
choose singular/plural form based on this value
.SS "Informative output:"
.TP
\fB\-h\fR, \fB\-\-help\fR
display this help and exit
.TP
\fB\-V\fR, \fB\-\-version\fR
display version information and exit
.PP
If the TEXTDOMAIN parameter is not given, the domain is determined from the
environment variable TEXTDOMAIN.  If the message catalog is not found in the
regular directory, another location can be specified with the environment
variable TEXTDOMAINDIR.
Standard search directory: /workspace/destdir/share/locale
.SH AUTHOR
Written by Ulrich Drepper.
.SH "REPORTING BUGS"
Report bugs in the bug tracker at <https://savannah.gnu.org/projects/gettext>
or by email to <bug\-<EMAIL>>.
.SH COPYRIGHT
Copyright \(co 1995\-1997, 2000\-2023 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>
.br
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.
.SH "SEE ALSO"
The full documentation for
.B ngettext
is maintained as a Texinfo manual.  If the
.B info
and
.B ngettext
programs are properly installed at your site, the command
.IP
.B info ngettext
.PP
should give you access to the complete manual.
