.\" Copyright (c) <PERSON> <<EMAIL>>
.\"
.\" This is free documentation; you can redistribute it and/or
.\" modify it under the terms of the GNU General Public License as
.\" published by the Free Software Foundation; either version 2 of
.\" the License, or (at your option) any later version.
.\"
.\" References consulted:
.\"   GNU glibc-2 source code and manual
.\"   GNU gettext source code and manual
.\"   LI18NUX 2000 Globalization Specification
.\"
.TH TEXTDOMAIN 3 "May 2001" "GNU gettext 0.22.2"
.SH NAME
textdomain \- set domain for future gettext() calls
.SH SYNOPSIS
.nf
.B #include <libintl.h>
.sp
.BI "char * textdomain (const char * " domainname );
.fi
.SH DESCRIPTION
The \fBtextdomain\fP function sets or retrieves the current message domain.
.PP
A message domain is a set of translatable \fImsgid\fP messages. Usually,
every software package has its own message domain. The domain name is used
to determine the message catalog where a translation is looked up; it must
be a non-empty string.
.PP
The current message domain is used by the \fBgettext\fP, \fBngettext\fP
functions, and by the \fBdgettext\fP, \fBdcgettext\fP, \fBdngettext\fP and
\fBdcngettext\fP functions when called with a NULL domainname argument.
.PP
If \fIdomainname\fP is not NULL, the current message domain is set to
\fIdomainname\fP. The string the function stores internally is a copy of the
\fIdomainname\fP argument.
.PP
If \fIdomainname\fP is NULL, the function returns the current message domain.
.SH "RETURN VALUE"
If successful, the \fBtextdomain\fP function returns the current message
domain, after possibly changing it. The resulting string is valid until the
next \fBtextdomain\fP call and must not be modified or freed. If a memory
allocation failure occurs, it sets \fBerrno\fP to \fBENOMEM\fP and returns
NULL.
.SH ERRORS
The following error can occur, among others:
.TP
.B ENOMEM
Not enough memory available.
.SH BUGS
The return type ought to be \fBconst char *\fP, but is \fBchar *\fP to avoid
warnings in C code predating ANSI C.
.SH "SEE ALSO"
.BR gettext (3),
.BR ngettext (3),
.BR bindtextdomain (3),
.BR bind_textdomain_codeset (3)
