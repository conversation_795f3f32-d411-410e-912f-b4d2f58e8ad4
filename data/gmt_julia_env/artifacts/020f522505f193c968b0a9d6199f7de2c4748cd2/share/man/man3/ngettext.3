.\" Copyright (c) <PERSON> <<EMAIL>>
.\"
.\" This is free documentation; you can redistribute it and/or
.\" modify it under the terms of the GNU General Public License as
.\" published by the Free Software Foundation; either version 2 of
.\" the License, or (at your option) any later version.
.\"
.\" References consulted:
.\"   GNU glibc-2 source code and manual
.\"   GNU gettext source code and manual
.\"   LI18NUX 2000 Globalization Specification
.\"
.TH NGETTEXT 3 "May 2001" "GNU gettext 0.22.2"
.SH NAME
ngettext, dngettext, dcngettext \- translate message and choose plural form
.SH SYNOPSIS
.nf
.B #include <libintl.h>
.sp
.BI "char * ngettext (const char * " msgid ", const char * " msgid_plural ,
.BI "                 unsigned long int " n );
.BI "char * dngettext (const char * " domainname ,
.BI "                  const char * " msgid ", const char * " msgid_plural ,
.BI "                  unsigned long int " n );
.BI "char * dcngettext (const char * " domainname ,
.BI "                   const char * " msgid ", const char * " msgid_plural ,
.BI "                   unsigned long int " n ", int " category );
.fi
.SH DESCRIPTION
The \fBngettext\fP, \fBdngettext\fP and \fBdcngettext\fP functions attempt to
translate a text string into the user's native language, by looking up the
appropriate plural form of the translation in a message catalog.
.PP
Plural forms are grammatical variants depending on the a number. Some languages
have two forms, called singular and plural. Other languages have three forms,
called singular, dual and plural. There are also languages with four forms.
.PP
The \fBngettext\fP, \fBdngettext\fP and \fBdcngettext\fP functions work like
the \fBgettext\fP, \fBdgettext\fP and \fBdcgettext\fP functions, respectively.
Additionally, they choose the appropriate plural form, which depends on the
number \fIn\fP and the language of the message catalog where the translation
was found.
.PP
In the "C" locale, or if none of the used catalogs contain a translation for
\fImsgid\fP, the \fBngettext\fP, \fBdngettext\fP and \fBdcngettext\fP functions
return \fImsgid\fP if \fIn\fP == 1, or \fImsgid_plural\fP if \fIn\fP != 1.
.SH "RETURN VALUE"
If a translation was found in one of the specified catalogs, the appropriate
plural form is converted to the locale's codeset and returned. The resulting
string is statically allocated and must not be modified or freed. Otherwise
\fImsgid\fP or \fImsgid_plural\fP is returned, as described above.
.SH ERRORS
\fBerrno\fP is not modified.
.SH BUGS
The return type ought to be \fBconst char *\fP, but is \fBchar *\fP to avoid
warnings in C code predating ANSI C.
.SH "SEE ALSO"
.BR gettext (3),
.BR dgettext (3),
.BR dcgettext (3)
