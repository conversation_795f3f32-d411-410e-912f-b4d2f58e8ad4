.\" Copyright (c) 1994  X Consortium
.\"
.\" Permission is hereby granted, free of charge, to any person obtaining a
.\" copy of this software and associated documentation files (the "Software"),
.\" to deal in the Software without restriction, including without limitation
.\" the rights to use, copy, modify, merge, publish, distribute, sublicense,
.\" and/or sell copies of the Software, and to permit persons to whom the
.\" Software furnished to do so, subject to the following conditions:
.\"
.\" The above copyright notice and this permission notice shall be included in
.\" all copies or substantial portions of the Software.
.\"
.\" THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
.\" IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
.\" FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
.\" THE X CONSORTIUM BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
.\" WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF
.\" OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
.\" SOFTWARE.
.\"
.\" Except as contained in this notice, the name of the X Consortium shall not
.\" be used in advertising or otherwise to promote the sale, use or other
.\" dealing in this Software without prior written authorization from the
.\" X Consortium.
.\"
.TH Xau 3 "libXau 1.0.12" "X Version 11"
.SH NAME
Xau library: XauFileName, XauReadAuth, XauLockAuth, XauUnlockAuth,
XauWriteAuth, XauDisposeAuth,
XauGetAuthByAddr, XauGetBestAuthByAddr \- X authority database routines
.SH SYNOPSIS
.ft CW
.nf
#include <X11/Xauth.h>

typedef struct xauth {
        unsigned short  family;
        unsigned short  address_length;
        char    *address;
        unsigned short  number_length;
        char    *number;
        unsigned short  name_length;
        char    *name;
        unsigned short  data_length;
        char    *data;
} Xauth;
.HP
char *XauFileName (void);
.HP
Xauth *XauReadAuth (FILE *\fIauth_file\fP\^);
.HP
int XauWriteAuth (FILE *\fIauth_file\fP, Xauth *\fIauth\fP\^);
.HP
Xauth *XauGetAuthByAddr (unsigned short \fIfamily\fP\^, unsigned short
\fIaddress_length\fP\^, const char *\fIaddress\fP\^, unsigned short
\fInumber_length\fP\^, const char *\fInumber\fP\^, unsigned short
\fIname_length\fP\^, const char *\fIname\fP\^);
.HP
Xauth *XauGetBestAuthByAddr (unsigned short \fIfamily\fP\^, unsigned short
\fIaddress_length\fP\^, const char *\fIaddress\fP\^, unsigned short
\fInumber_length\fP\^, const char *\fInumber\fP\^, int \fItypes_length\fP\^,
char **\fItypes\fR\^, const int *\fItype_lengths\fR\^);
.HP
int XauLockAuth (const char *\fIfile_name\fP\^, int \fIretries\fP\^, int
\fItimeout\fP\^, long \fIdead\fP\^);
.HP
int XauUnlockAuth (const char *\fIfile_name\fP\^);
.HP
int XauDisposeAuth (Xauth *\fIauth\fP\^);
.fi
.ft
.SH DESCRIPTION
.PP
\fBXauFileName\fP generates the default authorization file name by first
checking the XAUTHORITY environment variable if set, else it returns
$HOME/.Xauthority.  This name is statically allocated and should
not be freed.
.PP
\fBXauReadAuth\fP reads the next entry from \fIauth_file\fP.  The entry is
\fBnot\fP statically allocated and should be freed by calling
\fIXauDisposeAuth\fP.
.PP
\fBXauWriteAuth\fP writes an authorization entry to \fIauth_file\fP.  It
returns 1 on success, 0 on failure.
.PP
\fBXauGetAuthByAddr\fP searches for an entry which matches the given network
address/display number pair.  The entry is \fBnot\fP statically allocated
and should be freed by calling \fIXauDisposeAuth\fP.
.PP
\fBXauGetBestAuthByAddr\fP is similar to \fBXauGetAuthByAddr\fP, except
that a list of acceptable authentication methods is specified.  Xau will
choose the file entry which matches the earliest entry in this list (e.g., the
most secure authentication method).  The \fItypes\fP argument is an array of
strings, one string for each authentication method.  \fItypes_length\fP
specifies how many elements are in the \fItypes\fP array.
\fItypes_lengths\fP is an array of integers representing the length
of each string.
.PP
\fBXauLockAuth\fP does the work necessary to synchronously update an
authorization file.  First it makes two file names, one with ``-c'' appended
to \fIfile_name\fP, the other with ``-l'' appended.  If the ``-c'' file
already exists and is more than \fIdead\fP seconds old, \fIXauLockAuth\fP
removes it and the associated ``-l'' file.  To prevent possible
synchronization troubles with NFS, a \fIdead\fP value of zero forces the
files to be removed.  \fIXauLockAuth\fP makes \fIretries\fP attempts to
create and link the file names, pausing \fItimeout\fP seconds between each
attempt.  \fIXauLockAuth\fP returns a collection of values depending on the
results:
.TP
LOCK_ERROR
A system error occurred, either a file_name which is too long, or an
unexpected failure from a system call.  errno may prove useful.
.TP
LOCK_TIMEOUT
\fIretries\fP attempts failed
.TP
LOCK_SUCCESS
The lock succeeded.
.PP
\fBXauUnlockAuth\fP undoes the work of \fIXauLockAuth\fP by unlinking both
the ``-c'' and ``-l'' file names.
.PP
\fBXauDisposeAuth\fP frees storage allocated to hold an authorization entry.
.SH "SEE ALSO"
xauth(1), xdm(1)
.SH AUTHOR
Keith Packard, MIT X Consortium
