#!/usr/bin/env bash
#
#	Copyright (c) 1991-2025 by the GMT Team (https://www.generic-mapping-tools.org/team.html)
#	See LICENSE.TXT file for copying and redistribution conditions.
#
#	This program is free software; you can redistribute it and/or modify
#	it under the terms of the GNU Lesser General Public License as published by
#	the Free Software Foundation; version 3 or any later version.
#
#	This program is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU Lesser General Public License for more details.
#
#	Contact info: www.generic-mapping-tools.org
#-------------------------------------------------------------------------------
#   gmtswitch - switch between several installed GMT versions
#
is_numeric()
{ # Make sure argument is an integer
  if [[ "$1" != *[!0-9]* ]]; then
    return 0 # numeric
  fi
  return 1 # has a non-digit somewhere in it
}
exists()
{
  if ! [ -d "${1}/bin" ]; then
    # directory does not exist
    echo "ERROR: ${1}/bin does not exist" >&2
    exit 1
  fi
}
help=0
if [ $# -gt 1 ]; then
  help=1
fi
if [ $# -eq 1 ] && [ "X$1" = "X-help" ]; then
  help=1
fi
if [ $help -eq 1 ]; then
  cat << EOF >&2
usage: gmtswitch [version]

version, if present, is a unique text pattern identifying a GMT version,
e.g., GMT 5.1.1.  If none are given then we list available versions and
let the user choose one.  If the version is given as "init" OR it is the
very first time gmtswitch is run we will initialize the list of versions.
EOF
  exit
fi
#--------------------------------------------------------------------------------
# Get the functionality of echo -n
#--------------------------------------------------------------------------------
if [ x$(echo -n) = x ]; then	# echo -n works
  echon()
  {
    echo -n "$*"
  }
elif [ x$(echo -e) = x ]; then	# echo -e works
  echon()
  {
    echo -e "$*"'\c'
  }
else				# echo with escapes better work
  echon()
  {
    echo "$*"'\c'
  }
fi

cd ~
home=$(pwd)
if [ ! -f "$home/.gmtversions" ]; then	# No .gmtversions exists yet, first do that part
  cat << EOF >&2

GMTSWITCH

gmtswitch helps you modify your environment to allow for the switching back and
forth between several GMT versions, in particular GMT 5 and previous GMT
versions such as GMT 4.  First, we try to determine all the GMT versions
currently installed on this platform.  If you have not already installed the
latest GMT 4 please enter q at the prompt and do that install first.
Otherwise, hit RETURN and please be patient while we search your system.
Note: Searching from / can take hours on systems with lots of slow network
drives.  If that is your case, type q at the prompt to quit and instead
manually create the file ~/.gmtversions from your knowledge of what versions
are installed.  Each line in that file should be the full directory path to
a top-level GMT directory (i.e., one that contains share, bin, lib). Or,
you can type d and then be asked for what top level dir to start the search
from, e.g., /home/<USER>

EOF
  echon "==> Press RETURN to continue (or q RETURN to quit, or d RETURN to set top dir): " >&2
  read s
  if [ "x$s" = "xq" ]; then
	exit
  elif [ "x$s" = "x" ]; then
	top=/
  else
	  echon "==> Enter top dir: " >&2
	  read top
  fi
  echon "--> Searching..." >&2
  ( find "$top" -type d -name 'GMT[345].*' -print > "$home/.gmtversions" ) 2> /dev/null
  n=$(cat "$home/.gmtversions" | wc -l | awk '{print $1}')
  cat << EOF >&2

There are $n GMT versions currently installed on your system.
Before we can allow for switching you must prepare your environment.

1. CSH OR TCSH USERS
In your .cshrc or .tcshrc file:

Change your path statement. Make sure it includes $home/this_gmt/bin
If $home contains spaces places the above within double quotes.

2. BASH USERS
(re)Place these lines in your .profile file:

Change your PATH statement. Make sure it includes $home/this_gmt/bin
If $home contains spaces places the above within double quotes.

3. GitHub USERS:
You will have to manually add the path to the GMT directory since
it is not considered an official release.

After making these edits you must logout and back in again.
The next time you run gmtswitch you may do the switching.
EOF
  exit
fi

if [ "X$1" = "XD" ]; then	# First entry is default
  line=$(head -1 "$home/.gmtversions")
  exists "$line"
  rm -f "$home/this_gmt"
  ln -sf "$line" "$home/this_gmt"
elif [ $# -eq 1 ]; then
  line=$(grep $1 "$home/.gmtversions")
  nl=$(grep $1 "$home/.gmtversions" | wc -l)
  if [ $nl -eq 0 ]; then
    echo "Version $1 is not listed among recognized GMT versions" >&2
    echo "Run gmtswitch -help for more information" >&2
    exit
  fi
  if [ $nl -ne 1 ]; then
    echo "Version $1 is not unique among recognized GMT versions" >&2
    echo "Run gmtswitch without argument and choose from a menu" >&2
    exit
  fi
  exists "$line"
  rm -f "$home/this_gmt"
  ln -sf "$line" "$home/this_gmt"
else
  n=$(cat "$home/.gmtversions" | wc -l | awk '{print $1}')
  i=0
  while [ $i -lt $n ]; do
    i=$(expr $i + 1)
    line=$(sed -n 's/#.*//;'${i}p "$home/.gmtversions")
    comment=$(sed -n ${i}'s/.*# *\(.*\)/ (\1)/p' "$home/.gmtversions")
    version=$(basename $line)
    echo "$i. Select ${line}" >&2
  done
  echo "" >&2
  v=-1
  while [ $v -lt 0 ] || [ $v -gt $n ]; do
    echon "Please choose a GMT version (1-$n) [1]: " >&2
    read v
    if [ "X$v" = "X" ]; then
      v=1
    fi
    if ! is_numeric $v ; then
      echo "ERROR: You must give an integer from 1-$n" >&2
      v=-1
    fi
  done
  line=$(sed -n 's/#.*//;'${v}p "$home/.gmtversions")
  exists "$line"
  rm -f "$home/this_gmt"
  ln -sf "$line" "$home/this_gmt"
fi
