#!/usr/bin/env bash
#--------------------------------------------------------------------
# Copyright (c) 2015-2023 by <PERSON><PERSON>
# See LICENSE.TXT file for copying and redistribution conditions.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation; version 3 or any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# Contact info: http://www.soest.hawaii.edu/PT/GSFML
#--------------------------------------------------------------------
# <PERSON><PERSON><PERSON> to make a plot of the statistical parameters
# for a given FZ segment
#
# Author:	<PERSON>
# Date:		01-DEC-2023 (Requires GMT >= 6)
# Mode:		GMT classic mode
#--------------------------------------------------------------------

. fz_funcs.sh			# Load in FZ-specific shell functions
if [ $# -eq 0 ]; then
	version=`fz_get_version`
cat << EOF >&2
	fzinformer $version - Plot along-FZ statistical information
	
	Usage: fzinformer [-D] [-F<max>] [-I<FZid>][-N<max>] [-S<max>] [-T<prefix>] [-W<max>]

		OPTIONS:
		-D selected the filtered data [raw].
		-F sets max F amplitude (log scale used) [10000].
		-I select the FZ trace <ID> to report on [0 or first].
		-N sets max VGG amplitude [200].
		-S sets max +/- shift range [25].
		-T sets the profile prefix [fztrack].
		-V Report on progress [quiet].
		-W sets max W amplitude [50].
		
EOF
	exit
fi

trap 'fz_cleanup 1' 1 2 3 15	# Set interrup handling

# Default values
filtered=0
verbose=0
P=0
amax=200
fmax=10000
smax=25
wmax=50
prefix=fztrack

while [ ! x"$1" = x ]; do
	case $1
	in
		-D)	filtered=1;			# Used the filtered data
			shift;;
		-F*)	fmax=`fz_get_arg $1`		# max F
			shift;;
		-I*)	P=`fz_get_arg $1`		# profile argument
			shift;;
		-N*)	amax=`fz_get_arg $1`		# max amplitude
			shift;;
		-S*)	smax=`fz_get_arg $1`		# max +/- shift
			shift;;
		-T*)	prefix=`fz_get_arg $1`		# Get file prefix
			shift;;
		-W*)	wmax=`fz_get_arg $1`		# max width
			shift;;
		-V)	verbose=1;			# Gave a VGG grid
			shift;;
		-*)	echo "$0: Unrecognized option $1" 1>&2;	# Bad option argument
			exit 1;;
	esac
done

if [ ${filtered} -eq 0 ]; then
	AFILE=${prefix}_analysis.txt
else
	AFILE=${prefix}_filtered.txt
fi
BFILE=${prefix}_blend.txt
#-----------------------------------------
ymargin=1
xmargin=1
spacing=0.175
W=6.3
H=1.2
xu=`gmt math -Q -0.75 1 ${xmargin} SUB ADD =`
yu=`gmt math -Q -0.75 1 ${ymargin} SUB ADD =`
# Extract our segment */
gmt convert -Q${P} ${AFILE} > /tmp/t.txt
# How many points along segment?
n_cross=`cat /tmp/t.txt | wc -l | awk '{printf "%d\n", $1-1}'`
yup=`gmt math -Q ${H} ${spacing} ADD =`
gmt info ${AFILE} -C -I1/1/2/10/0.01/1/1/5/5/5/1 > $$.info
dmin=`cut -f5 $$.info`
dmax=`cut -f6 $$.info`
gmt begin ${prefix}_stat pdf
	gmt basemap -R${dmin}/${dmax}/0/1 -JX6i/8.9i -U/${xu}i/${yu}i/"${prefix}" -K -B+gwhite -X${xmargin}i -Y${ymargin}i
	# Plot blend and u values
	x=`fz_col_id DR`
	y=`fz_col_id BL`
	gmt plot ${AFILE} -i${x},${y} -R${dmin}/${dmax}/-0.05/1.05 -JX${W}i/${H}i -Bxa500f100+l"Distance along FZ"+u"km" -Bya0.2f0.1 -BWSnE -Y0.25i -W1p --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14 --MAP_LABEL_OFFSET=0.05i
	y=`fz_col_id UT`
	gmt plot ${AFILE} -i${x},${y} -W1p,green
	y=`fz_col_id UB`
	gmt plot ${AFILE} -i${x},${y} -W1p,blue
	echo "${dmax} 1.05 C" | gmt text-Gwhite -W0.25p  -F+jTR+f9p -Dj0.05i
	echo "${dmin} 1.05 A" | gmt text -Gwhite -W0.25p  -F+jTL+f9p -Dj0.05i
	# Plot directions
	y=`fz_col_id OR`
	gmt plot $AFI{LE -i${x},${y} -R${dmin}/${dmax}/-1.1/1.1 -Bxa500f100 -Bya1g5 -BWsnE -W1p -Y${yup}i --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14 --MAP_LABEL_OFFSET=0.05i
	echo "${dmax} 0 O" | gmt text Gwhite -W0.25p  -F+jBR+f9p -Dj0.05i
	# Plot offsets
	y=`fz_col_id SD`
	gmt plot ${AFILE} -i${x},${y} -R${dmin}/${dmax}/-${smax}/${smax} -Bxa500f100 -Bya5f1g100 -BWsnE -W1p,red -Y${yup}i --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14
	y=`fz_col_id ST`
	gmt plot ${AFILE} -i${x},${y} -W1p,green
	y=`fz_col_id SB`
	gmt plot ${AFILE} -i${x},${y} -W1p,blue
	echo "$dmax $smax @~D@~" | gmt text -Gwhite -W0.25p -F+jTR+f9p -Dj0.05i
	# Plot widths
	y=`fz_col_id WD`
	gmt plot ${AFILE} -i${x},${y} -R${dmin}/${dmax}/0/${wmax} -Bxa500f100 -Bya10f5 -BWsnE -W1p,red -Y${yup}i --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14
	y=`fz_col_id WT`
	gmt plot ${AFILE} -i${x},${y} -W1p,green
	y=`fz_col_id WB`
	gmt plot ${AFILE} -i${x},${y} -W1p,blue
	echo "${dmax} ${wmax} W" | gmt text -Gwhite -W0.25p -F+jTR+f9p -Dj0.05i
	gmt plot -W0.5p,- << EOF
	${dmin}	15
	${dmax}	15
	EOF
	# Plot amplitudes
	y=`fz_col_id AD`
	gmt plot ${AFILE} -i${x},${y} -R${dmin}/${dmax}/0/${amax} -Bxa500f100 -Bya50f10g1000 -BWsnE -W1p,red -Y${yup}i --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14
	y=`fz_col_id AT`
	gmt plot ${AFILE} -i${x},${y} -W1p,green
	y=`fz_col_id AB`
	gmt plot ${AFILE} -i${x},${y}-W1p,blue
	echo "${dmax} ${amax} A" | gmt text-Gwhite -W0.25p  -F+jTR+f9p -Dj0.05i
	gmt plot -W0.5p,- << EOF
	${dmin}	25
	${dmax}	25
	EOF
	# Plot variance reductions
	y=`fz_col_id VT`
	gmt plot ${AFILE} -i${x},${y} -R${dmin}/${dmax}/-5/105 -JX${W}i/${H}i -Bxa500f100 -Bya20f5 -BWsnE -W1p,green -Y${yup}i --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14
	y=`fz_col_id VB`
	gmt plot ${AFILE} -i${x},${y} -W1p,blue
	echo "${dmax} 105 V" | gmt text -Gwhite -W0.25p -F+jTR+f9p -Dj0.05i
	gmt plot -W0.5p,- << EOF
	${dmin}	50
	${dmax}	50
	EOF
	# Plot F values
	y=`fz_col_id FT`
	gmt plot ${AFILE} -i${x},${y} -R${dmin}/${dmax}/1/${fmax} -JX${W}i/${H}il -Bxa500f100 -Bya1pf3 -BWsE -W1p,green -Y${yup}i --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14
	echo "${dmax} ${fmax} F" | gmt text -Gwhite -W0.25p -F+jTR+f9p -Dj0.05i
	y=`fz_col_id FB`
	gmt plot ${AFILE} -i${x},${y} -W1p,blue
	gmt plot -W0.5p,- << EOF
	${dmin}	50
	${dmax}	50
	EOF
	#grep -v '^>' ${AFILE} | cut -f3,11 | gmt plot -R${dmin}/${dmax}/0/4 -Bxa500f100 -Bya1f1+l"Q" -BWse -W1p -Y${yup}i --FONT_ANNOT_PRIMARY=10
	gmt basemap -R0/${n_cross}/0/100 -Bxa20f1+l"FZ ID ${P}" -By0 -BN --FONT_ANNOT_PRIMARY=10 --FONT_LABEL=14 --MAP_LABEL_OFFSET=0.05i
	if [ $verbose -eq 1 ]; then
		echo "fzinformer: Final plot is called ${prefix}_stat.pdf" >&2
	fi
gmt end show

fz_cleanup 1
