#!/usr/bin/env bash
#--------------------------------------------------------------------
#
#  Copyright (c) 1991-2025 by the GMT Team (https://www.generic-mapping-tools.org/team.html)
#  See LICENSE.TXT file for copying and redistribution conditions.
#
#  This program is free software; you can redistribute it and/or modify
#  it under the terms of the GNU Lesser General Public License as published by
#  the Free Software Foundation; version 3 or any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU Lesser General Public License for more details.
#
#  Contact info: www.generic-mapping-tools.org
#
# gmt-config simply reports various paths and settings that are useful
# to developers that need to include/link GMT resources.
#--------------------------------------------------------------------*/
#
GMT_EXEDIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Use the executable dir to figure out the lib and include dirs
GMT_TOP=$(dirname $GMT_EXEDIR)
CONFIG_INCLUDEDIR="$GMT_TOP/include/gmt"
# Check if the configure include directory exist.  If so then we craft
# the cflags and lib settings off its parent, else stick with config.
if [ -d $CONFIG_INCLUDEDIR ]; then
  CONFIG_CFLAGS="-I$CONFIG_INCLUDEDIR"
  CONFIG_LIBDIR="$GMT_TOP/lib"
else
  CONFIG_CFLAGS="-I/workspace/destdir/include/gmt"
  CONFIG_INCLUDEDIR="/workspace/destdir/include/gmt"
  CONFIG_LIBDIR="/workspace/destdir/lib"
fi
CONFIG_LIBS="-L$CONFIG_LIBDIR -lgmt"
CONFIG_CC="/opt/bin/x86_64-linux-gnu-libgfortran5-cxx11/x86_64-linux-gnu-gcc"
CONFIG_DATADIR=$($GMT_EXEDIR/gmt --show-datadir)
CONFIG_DATASERVER=$($GMT_EXEDIR/gmt --show-dataserver)
CONFIG_PLUGINDIR=$($GMT_EXEDIR/gmt --show-plugindir)
CONFIG_DEP_LIBS="/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libnetcdf.so /opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libcurl.so;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libgdal.so;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libgeos_c.so;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libpcre.so;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libfftw3f.so;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libopenblas.so;-lpthread;-lm;-ldl;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libopenblas.so;/opt/x86_64-linux-gnu/x86_64-linux-gnu/sys-root/usr/local/lib/libz.so;OpenMP::OpenMP_C"
CONFIG_GSHHG=$($GMT_EXEDIR/gmt gmtget DIR_GSHHG)
CONFIG_DCW=$($GMT_EXEDIR/gmt gmtget DIR_DCW)
CONFIG_PREFIX="/workspace/destdir"
CONFIG_VERSION="6.6.0_1ed5e1b_2025.06.23"

if [ "TRUE" == "TRUE" ]; then
  CONFIG_FFTW_ENABLED=yes
else
  CONFIG_FFTW_ENABLED=no
fi

if [ "TRUE" == "TRUE" ] || [ "" == "TRUE" ]; then
  CONFIG_PCRE_ENABLED=yes
else
  CONFIG_PCRE_ENABLED=no
fi

if [ "TRUE" == "TRUE" ]; then
  CONFIG_LAPACK_ENABLED=yes
else
  CONFIG_LAPACK_ENABLED=no
fi

if [ "disabled" == "enabled" ]; then
  CONFIG_GTHREAD_ENABLED=yes
else
  CONFIG_GTHREAD_ENABLED=no
fi

if [ "enabled" == "enabled" ]; then
  CONFIG_OPENMP_ENABLED=yes
else
  CONFIG_OPENMP_ENABLED=no
fi

if [ 8 -eq 8 ]; then
  CONFIG_BITS=64
else
  CONFIG_BITS=32
fi

usage()
{
  cat <<EOF
Usage: gmt-config [OPTIONS]

Available values for OPTIONS include:

  --help         display this help message and exit
  --all          display all options
  --bits         whether library is build 32-bit or 64-bit
  --cc           C compiler
  --cflags       pre-processor and compiler flags
  --datadir      GMT's data directories
  --dataserver   URL of the remote GMT data server
  --dcw          location of used DCW
  --dep-libs     dependent libraries
  --gshhg        location of used GSHHG
  --has-fftw     whether FFTW is used in build
  --has-pcre     whether PCRE is used in build
  --has-lapack   whether LAPACK is used in build
  --has-openmp   whether GMT was built with OpenMP support
  --has-gthread  whether GMT was built with GTHREAD support
  --includedir   include directory
  --libdir       library directory
  --libs         library linking information
  --plugindir    GMT's plugin directory
  --prefix       install prefix
  --version      library version
EOF
  exit $1
}

all()
{
  cat <<EOF

This GMT $CONFIG_VERSION has been built with the following features:

  --cc          -> $CONFIG_CC
  --cflags      -> $CONFIG_CFLAGS
  --libs        -> $CONFIG_LIBS

  --bits        -> $CONFIG_BITS
  --datadir     -> $CONFIG_DATADIR
  --dataserver  -> $CONFIG_DATASERVER
  --dcw         -> $CONFIG_DCW
  --dep-libs    -> ${CONFIG_DEP_LIBS//;/ }
  --gshhg       -> $CONFIG_GSHHG
  --has-fftw    -> $CONFIG_FFTW_ENABLED
  --has-pcre    -> $CONFIG_PCRE_ENABLED
  --has-lapack  -> $CONFIG_LAPACK_ENABLED
  --has-openmp  -> $CONFIG_OPENMP_ENABLED
  --has-gthred  -> $CONFIG_GTHREAD_ENABLED

  --prefix      -> $CONFIG_PREFIX
  --includedir  -> $CONFIG_INCLUDEDIR
  --libdir      -> $CONFIG_LIBDIR
  --plugindir   -> $CONFIG_PLUGINDIR
  --version     -> $CONFIG_VERSION

EOF
}

[ -z "$1" ] && usage 1 1>&2

for arg in "$@"; do
  case $arg in
    --all)
    all
    ;;

    --bits)
    echo $CONFIG_BITS
    ;;

    --cc)
    echo $CONFIG_CC
    ;;

    --cflags)
    echo $CONFIG_CFLAGS
    ;;

    --datadir)
    echo $CONFIG_DATADIR
    ;;

    --dataserver)
    echo $CONFIG_DATASERVER
    ;;

    --dcw)
    echo $CONFIG_DCW
    ;;

    --dep-libs)
    echo ${CONFIG_DEP_LIBS//;/ }
    ;;

    --gshhg)
    echo $CONFIG_GSHHG
    ;;

    --has-fftw)
    echo $CONFIG_FFTW_ENABLED
    ;;

    --has-pcre)
    echo $CONFIG_PCRE_ENABLED
    ;;

    --has-lapack)
    echo $CONFIG_LAPACK_ENABLED
    ;;

    --has-openmp)
    echo $CONFIG_OPENMP_ENABLED
    ;;

    --has-gthread)
    echo $CONFIG_GTHREAD_ENABLED
    ;;

    --includedir)
    echo $CONFIG_INCLUDEDIR
    ;;

    --libdir)
    echo $CONFIG_LIBDIR
    ;;

    --libs)
    echo $CONFIG_LIBS
    ;;

    --plugindir)
    echo $CONFIG_PLUGINDIR
    ;;

    --prefix)
    echo $CONFIG_PREFIX
    ;;

    --version)
    echo $CONFIG_VERSION
    ;;

    *)
    usage 1 1>&2
    ;;
  esac
done
