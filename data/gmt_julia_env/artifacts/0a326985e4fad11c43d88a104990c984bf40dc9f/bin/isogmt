#!/usr/bin/env bash
#--------------------------------------------------------------------
#
#	Copyright (c) 1991-2025 by the GMT Team (https://www.generic-mapping-tools.org/team.html)
#	See LICENSE.TXT file for copying and redistribution conditions.
#
#	This program is free software; you can redistribute it and/or modify
#	it under the terms of the GNU Lesser General Public License as published by
#	the Free Software Foundation; version 3 or any later version.
#
#	This program is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU Lesser General Public License for more details.
#
#	Contact info: www.generic-mapping-tools.org
#--------------------------------------------------------------------
#
# isogmt runs a GMT command or script in isolation mode.

if [ $# -eq 0 ]; then
	cat << END >&2
isogmt 6.6.0_1ed5e1b_2025.06.23 - Run GMT command or script in isolation mode

usage: isogmt <command>

	<command> is a single GMT command plus options or an executable script.
END
	exit
fi

export GMT_TMPDIR=$(mktemp -d ${TMPDIR:-/tmp}/gmt.XXXXXX)
gmt $1 =
if test $? -eq 0; then
	gmt "$@"
else
	"$@"
fi
rm -rf $GMT_TMPDIR
unset GMT_TMPDIR
