using GMT

# 假设你已经有了 Cvp 和 Cvs 数据立方体
# 这里是保存 cubeplot 结果的不同方法示例

# 方法1: 使用 figname 参数保存为 PNG（推荐）
cubeplot!(Cvs, top="@earth_relief_05m", topshade=true, zdown=true, 
          colorbar=("xlabel=S-wave velocity", "ylabel=km/s"), 
          xshift=18, title="Vs model", figname="Vs_model.png")

# 方法2: 使用 figname 参数保存为 PDF
cubeplot!(Cvs, top="@earth_relief_05m", topshade=true, zdown=true, 
          colorbar=("xlabel=S-wave velocity", "ylabel=km/s"), 
          xshift=18, title="Vs model", figname="Vs_model.pdf")

# 方法3: 使用 savefig 参数
cubeplot(Cvp, top="@earth_relief", inset=(lon=100, lat=35), topshade=true, zdown=true,
         colorbar=("xlabel=P-wave velocity", "ylabel=km/s"), 
         savefig="Vp_model_inset.png")

# 方法4: 指定格式和文件名
cubeplot(Cvp, top="@earth_relief", inset=(lon=100, lat=35), topshade=true, zdown=true,
         colorbar=("xlabel=P-wave velocity", "ylabel=km/s"), 
         figname="Vp_model_inset", fmt=:png)

# 方法5: 保存但不显示（适合批处理）
cubeplot(Cvp, top="@earth_relief", inset=(lon=100, lat=35), topshade=true, zdown=true,
         colorbar=("xlabel=P-wave velocity", "ylabel=km/s"), 
         figname="Vp_model_inset_no_show.png", show=false)

# 方法6: 保存为多种格式
cubeplot(Cvp, top="@earth_relief", inset=(lon=100, lat=35), topshade=true, zdown=true,
         colorbar=("xlabel=P-wave velocity", "ylabel=km/s"), 
         figname="Vp_model_inset", fmt="png,pdf")
